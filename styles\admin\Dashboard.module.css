/* Admin Dashboard Styles */

.dashboard {
  min-height: 100vh;
  /* background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); */ /* Removed */
  /* font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif; */ /* Removed */
}

.header {
  background: var(--admin-bg-primary); /* Changed */
  /* backdrop-filter: blur(10px); */ /* Removed */
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--admin-shadow-light); /* Changed */
  border-bottom: 1px solid var(--admin-border-light); /* Changed */
}

.title {
  font-size: 1.8rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.userInfo {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.userEmail {
  color: var(--admin-gray); /* Changed */
  font-weight: 500;
}

.logoutButton {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: var(--admin-radius-md); /* Changed */
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.logoutButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

.tabs {
  background: var(--admin-bg-primary); /* Changed */
  /* backdrop-filter: blur(10px); */ /* Removed */
  padding: 0 2rem;
  display: flex;
  gap: 0.5rem;
  border-bottom: 1px solid var(--admin-border-light); /* Changed */
  overflow-x: auto;
}

.tabButton {
  background: none;
  border: none;
  padding: 1rem 1.5rem;
  font-weight: 500;
  color: var(--admin-gray); /* Changed */
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
  white-space: nowrap;
  text-decoration: none;
  display: inline-block;
}

.tabButton:hover {
  color: var(--admin-primary); /* Changed */
  background: var(--admin-bg-tertiary); /* Changed */
}

.tabButton.active {
  color: var(--admin-primary); /* Changed */
  border-bottom-color: var(--admin-primary); /* Changed */
  background: var(--admin-bg-tertiary); /* Changed */
}

.tabContent {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.tabHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.tabTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
  margin: 0;
}

.tabActions {
  display: flex;
  gap: 1rem;
}

.newButton, .refreshButton {
  /* background: linear-gradient(135deg, #10b981, #059669); */ /* Removed */
  background: var(--admin-success); /* Changed */
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--admin-radius-md); /* Changed */
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
}

.newButton:hover, .refreshButton:hover {
  transform: translateY(-2px);
  /* box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4); */ /* Consider var(--admin-shadow-medium) or similar */
}

.refreshButton {
  /* background: linear-gradient(135deg, #3b82f6, #1d4ed8); */ /* Removed */
  background: var(--admin-info); /* Changed */
}

.refreshButton:hover {
  /* box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4); */ /* Consider var(--admin-shadow-medium) or similar */
}

/* Overview Tab Styles */
.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.statCard {
  background: var(--admin-bg-primary); /* Changed */
  /* backdrop-filter: blur(10px); */ /* Removed */
  padding: 1.5rem;
  border-radius: var(--admin-radius-lg); /* Changed */
  box-shadow: var(--admin-shadow-light); /* Changed */
  border: 1px solid var(--admin-border-light); /* Changed */
}

.statCard h3 {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--admin-gray); /* Changed */
  margin: 0 0 0.5rem 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.statValue {
  font-size: 2rem;
  font-weight: 700;
  color: var(--admin-darker); /* Changed */
  margin-bottom: 0.25rem;
}

.statChange {
  font-size: 0.875rem;
  color: var(--admin-success); /* Assuming positive change, can be dynamic */
  font-weight: 500;
}

.quickActions {
  background: var(--admin-bg-primary); /* Changed */
  /* backdrop-filter: blur(10px); */ /* Removed */
  padding: 2rem;
  border-radius: var(--admin-radius-lg); /* Changed */
  box-shadow: var(--admin-shadow-light); /* Changed */
  border: 1px solid var(--admin-border-light); /* Changed */
}

.quickActions h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--admin-darker); /* Changed */
  margin: 0 0 1.5rem 0;
}

.actionGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.actionCard {
  background: var(--admin-bg-primary); /* Changed */
  border: 1px solid var(--admin-border-light); /* Changed */
  padding: 1.5rem;
  border-radius: var(--admin-radius-md); /* Changed */
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  color: inherit;
  display: block;
  box-shadow: var(--admin-shadow-light); /* Added */
}

.actionCard:hover {
  transform: translateY(-2px);
  box-shadow: var(--admin-shadow-medium); /* Changed */
  background: var(--admin-bg-tertiary); /* Changed */
}

.actionCard h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--admin-darker); /* Changed */
  margin: 0 0 0.5rem 0;
}

.actionCard p {
  font-size: 0.875rem;
  color: var(--admin-gray); /* Changed */
  margin: 0;
}

/* Coming Soon Placeholder */
.comingSoon {
  background: var(--admin-bg-primary); /* Changed */
  /* backdrop-filter: blur(10px); */ /* Removed */
  padding: 3rem;
  border-radius: var(--admin-radius-lg); /* Changed */
  box-shadow: var(--admin-shadow-light); /* Changed */
  border: 1px solid var(--admin-border-light); /* Changed */
  text-align: center;
}

.comingSoon h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--admin-darker); /* Changed */
  margin: 0 0 1rem 0;
}

.comingSoon p {
  color: var(--admin-gray); /* Changed */
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.comingSoon ul {
  text-align: left;
  max-width: 400px;
  margin: 1.5rem auto 0;
  color: var(--admin-gray); /* Changed */
}

.comingSoon li {
  margin-bottom: 0.5rem;
}

/* Loading and Auth States */
.loadingContainer, .authContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: var(--admin-bg-secondary); /* Changed */
  color: var(--admin-darker); /* Changed */
  text-align: center;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--admin-primary-light); /* Changed */
  border-top: 4px solid var(--admin-primary); /* Changed */
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.authContainer h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.authContainer p {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.loginButton {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 1rem 2rem;
  border-radius: 8px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
}

.loginButton:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

/* Grid Layout */
.grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1.5rem;
  margin-top: 2rem;
}

.leftColumn {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.rightColumn {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Responsive Design - Enhanced scaling */
@media (min-width: 768px) {
  .dashboard {
    padding: 1rem 2rem;
  }
  
  .header {
    flex-direction: row;
    align-items: center;
    padding: 1rem 2rem;
  }
  
  .grid {
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
  }
}

@media (min-width: 1024px) {
  .dashboard {
    max-width: 1400px;
    margin: 0 auto;
  }
  
  .grid {
    gap: 2.5rem;
  }
}

/* Fix for overscaling */
html, body {
  font-size: 16px;
}

* {
  box-sizing: border-box;
}

/* Ensure components don't exceed viewport */
.analyticsContainer,
.chartCard,
.metricCard {
  max-width: 100%;
  overflow: hidden;
}

/* New section for analytics */
.analyticsSection {
  margin-bottom: 2rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}
