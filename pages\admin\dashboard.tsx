import { useState, useEffect } from 'react';
import Head from 'next/head';
import DashboardStats from '../../components/admin/DashboardStats';
import RecentBookings from '../../components/admin/RecentBookings';
import QuickActions from '../../components/admin/QuickActions';
import ActivityFeed from '../../components/admin/ActivityFeed';
import { useAuth } from '../../hooks/useAuth';
import styles from '../../styles/admin/Dashboard.module.css';

interface DashboardData {
  stats: {
    totalBookings: number;
    totalRevenue: number;
    activeCustomers: number;
    pendingBookings: number;
    completedBookings: number;
    cancelledBookings: number;
    averageBookingValue: number;
    monthlyGrowth: number;
  };
  recentBookings: any[];
  recentActivity: any[];
}

export default function AdminDashboard() {
  const { user } = useAuth();
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('admin-token');
      
      const response = await fetch('/api/admin/dashboard', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to load dashboard data');
      }

      const data = await response.json();
      setDashboardData(data);
    } catch (error) {
      console.error('Dashboard error:', error);
      setError(error instanceof Error ? error.message : 'Failed to load dashboard');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <>
        <Head>
          <title>Loading Dashboard - Ocean Soul Sparkles</title>
        </Head>
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>Loading dashboard...</p>
        </div>
      </>
    );
  }

  if (error) {
    return (
      <>
        <Head>
          <title>Error - Ocean Soul Sparkles</title>
        </Head>
        <div className={styles.error}>
          <h2>Error Loading Dashboard</h2>
          <p>{error}</p>
          <button onClick={loadDashboardData} className={styles.retryButton}>
            Try Again
          </button>
        </div>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>Admin Dashboard - Ocean Soul Sparkles</title>
      </Head>

      <div className={styles.dashboard}>
        <div className={styles.header}>
          <h1>Welcome back, {user?.firstName}!</h1>
          <p>Here's what's happening with your business today.</p>
        </div>        {dashboardData && (
          <>
            <DashboardStats data={dashboardData.stats} />
            
            <div className={styles.grid}>
              <div className={styles.leftColumn}>
                <RecentBookings 
                  bookings={dashboardData.recentBookings} 
                  userRole={user?.role || 'Admin'} 
                />
                <QuickActions userRole={user?.role || 'Admin'} />
              </div>
              
              <div className={styles.rightColumn}>
                <ActivityFeed 
                  activities={dashboardData.recentActivity} 
                  userRole={user?.role || 'Admin'} 
                />
              </div>
            </div>
          </>
        )}
      </div>
    </>
  );
}
